---
title: Intro
description: Get started with opencode.
---

import { Tabs, TabItem } from "@astrojs/starlight/components"

[**opencode**](/) is an AI coding agent built for the terminal.

![opencode TUI with the opencode theme](../../../assets/lander/screenshot.png)

Let's get started.

---

#### Prerequisites

To use opencode, you'll need:

1. A modern terminal emulator like:

   - [WezTerm](https://wezterm.org), cross-platform
   - [Alacritty](https://alacritty.org), cross-platform
   - [Ghostty](https://ghostty.org), Linux and macOS
   - [Kitty](https://sw.kovidgoyal.net/kitty/), Linux and macOS

2. API keys for the LLM providers you want to use.

---

## Install

The easiest way to install opencode is through the install script.

```bash
curl -fsSL https://opencode.ai/install | bash
```

You can also install it with the following:

- **Using Node.js**

  <Tabs>
    <TabItem label="npm">
    ```bash
    npm install -g opencode-ai
    ```
    </TabItem>
    <TabItem label="Bun">
    ```bash
    bun install -g opencode-ai
    ```
    </TabItem>
    <TabItem label="pnpm">
    ```bash
    pnpm install -g opencode-ai
    ```
    </TabItem>
    <TabItem label="Yarn">
    ```bash 
    yarn global add opencode-ai
    ```
    </TabItem>
  </Tabs>

- **Using Homebrew on macOS and Linux**

  ```bash
  brew install sst/tap/opencode
  ```

- **Using Paru on Arch Linux**

  ```bash
  paru -S opencode-bin
  ```

#### Windows

Right now the automatic installation methods do not work properly on Windows. However you can grab the binary from the [Releases](https://github.com/sst/opencode/releases).

---

## Configure

With opencode you can use any LLM provider by configuring their API keys.

We recommend signing up for [Claude Pro](https://www.anthropic.com/news/claude-pro) or [Max](https://www.anthropic.com/max), it's the most cost-effective way to use opencode.

Once you've signed up, run `opencode auth login` and select Anthropic.

```bash
$ opencode auth login

┌  Add credential
│
◆  Select provider
│  ● Anthropic (recommended)
│  ○ OpenAI
│  ○ Google
│  ○ Amazon Bedrock
│  ○ Azure
│  ○ DeepSeek
│  ○ Groq
│  ...
└
```

Alternatively, you can select one of the other providers. [Learn more](/docs/providers#directory).

---

## Initialize

Now that you've configured a provider, you can navigate to a project that
you want to work on.

```bash
cd /path/to/project
```

And run opencode.

```bash
opencode
```

Next, initialize opencode for the project by running the following command.

```bash frame="none"
/init
```

This will get opencode to analyze your project and create an `AGENTS.md` file in
the project root.

:::tip
You should commit your project's `AGENTS.md` file to Git.
:::

This helps opencode understand the project structure and the coding patterns
used.

---

## Usage

You are now ready to use opencode to work on your project. Feel free to ask it
anything!

If you are new to using an AI coding agent, here are some examples that might
help.

---

### Ask questions

You can ask opencode to explain the codebase to you.

:::tip
Use the `@` key to fuzzy search for files in the project.
:::

```txt frame="none" "@packages/functions/src/api/index.ts"
How is authentication handled in @packages/functions/src/api/index.ts
```

This is helpful if there's a part of the codebase that you didn't work on.

---

### Add features

You can ask opencode to add new features to your project. Though we first recommend asking it to create a plan.

1. **Create a plan**

   opencode has a _Plan mode_ that disables its ability to make changes and
   instead suggest _how_ it'll implement the feature.

   Switch to it using the **Tab** key. You'll see an indicator for this in the lower right corner.

   ```bash frame="none" title="Switch to Plan mode"
   <TAB>
   ```

   Now let's describe what we want it to do.

   ```txt frame="none"
   When a user deletes a note, we'd like to flag it as deleted in the database.
   Then create a screen that shows all the recently deleted notes.
   From this screen, the user can undelete a note or permanently delete it.
   ```

   You want to give opencode enough details to understand what you want. It helps
   to talk to it like you are talking to a junior developer on your team.

   :::tip
   Give opencode plenty of context and examples to help it understand what you
   want.
   :::

2. **Iterate on the plan**

   Once it gives you a plan, you can give it feedback or add more details.

   ```txt frame="none"
   We'd like to design this new screen using a design I've used before.
   [Image #1] Take a look at this image and use it as a reference.
   ```

   :::tip
   Drag and drop images into the terminal to add them to the prompt.
   :::

   opencode can scan any images you give it and add them to the prompt. You can
   do this by dragging and dropping an image into the terminal.

3. **Build the feature**

   Once you feel comfortable with the plan, switch back to _Build mode_ by
   hitting the **Tab** key again.

   ```bash frame="none"
   <TAB>
   ```

   And asking it to make the changes.

   ```bash frame="none"
   Sounds good! Go ahead and make the changes.
   ```

---

### Make changes

For more straightforward changes, you can ask opencode to directly build it
without having to review the plan first.

```txt frame="none" "@packages/functions/src/settings.ts" "@packages/functions/src/notes.ts"
We need to add authentication to the /settings route. Take a look at how this is
handled in the /notes route in @packages/functions/src/notes.ts and implement
the same logic in @packages/functions/src/settings.ts
```

You want to make sure you provide a good amount of detail so opencode makes the right
changes.

---

### Undo changes

Let's say you ask opencode to make some changes.

```txt frame="none" "@packages/functions/src/api/index.ts"
Can you refactor the function in @packages/functions/src/api/index.ts?
```

But you realize that it is not what you wanted. You **can undo** the changes
using the `/undo` command.

```bash frame="none"
/undo
```

opencode will now revert the changes you made and show your original message
again.

```txt frame="none" "@packages/functions/src/api/index.ts"
Can you refactor the function in @packages/functions/src/api/index.ts?
```

From here you can tweak the prompt and ask opencode to try again.

:::tip
You can run `/undo` multiple times to undo multiple changes.
:::

Or you **can redo** the changes using the `/redo` command.

```bash frame="none"
/redo
```

---

## Share

The conversations that you have with opencode can be [shared with your
team](/docs/share).

```bash frame="none"
/share
```

This will create a link to the current conversation and copy it to your clipboard.

:::note
Conversations are not shared by default.
:::

Here's an [example conversation](https://opencode.ai/s/4XP1fce5) with opencode.

---

## Customize

And that's it! You are now a pro at using opencode.

To make it your own, we recommend [picking a theme](/docs/themes), [customizing the keybinds](/docs/keybinds), [configuring code formatters](/docs/formatters), [creating custom commands](/docs/commands), or playing around with the [opencode config](/docs/config).
