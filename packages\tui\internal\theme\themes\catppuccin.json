{"$schema": "https://opencode.ai/theme.json", "defs": {"lightRosewater": "#dc8a78", "lightFlamingo": "#dd7878", "lightPink": "#ea76cb", "lightMauve": "#8839ef", "lightRed": "#d20f39", "lightMaroon": "#e64553", "lightPeach": "#fe640b", "lightYellow": "#df8e1d", "lightGreen": "#40a02b", "lightTeal": "#179299", "lightSky": "#04a5e5", "lightSapphire": "#209fb5", "lightBlue": "#1e66f5", "lightLavender": "#7287fd", "lightText": "#4c4f69", "lightSubtext1": "#5c5f77", "lightSubtext0": "#6c6f85", "lightOverlay2": "#7c7f93", "lightOverlay1": "#8c8fa1", "lightOverlay0": "#9ca0b0", "lightSurface2": "#acb0be", "lightSurface1": "#bcc0cc", "lightSurface0": "#ccd0da", "lightBase": "#eff1f5", "lightMantle": "#e6e9ef", "lightCrust": "#dce0e8", "darkRosewater": "#f5e0dc", "darkFlamingo": "#f2cdcd", "darkPink": "#f5c2e7", "darkMauve": "#cba6f7", "darkRed": "#f38ba8", "darkMaroon": "#eba0ac", "darkPeach": "#fab387", "darkYellow": "#f9e2af", "darkGreen": "#a6e3a1", "darkTeal": "#94e2d5", "darkSky": "#89dceb", "darkSapphire": "#74c7ec", "darkBlue": "#89b4fa", "darkLavender": "#b4befe", "darkText": "#cdd6f4", "darkSubtext1": "#bac2de", "darkSubtext0": "#a6adc8", "darkOverlay2": "#9399b2", "darkOverlay1": "#7f849c", "darkOverlay0": "#6c7086", "darkSurface2": "#585b70", "darkSurface1": "#45475a", "darkSurface0": "#313244", "darkBase": "#1e1e2e", "darkMantle": "#181825", "darkCrust": "#11111b"}, "theme": {"primary": {"dark": "darkBlue", "light": "lightBlue"}, "secondary": {"dark": "darkMauve", "light": "lightMauve"}, "accent": {"dark": "darkPink", "light": "lightPink"}, "error": {"dark": "darkRed", "light": "lightRed"}, "warning": {"dark": "<PERSON><PERSON><PERSON><PERSON>", "light": "light<PERSON>ellow"}, "success": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGreen"}, "info": {"dark": "darkTeal", "light": "lightTeal"}, "text": {"dark": "darkText", "light": "lightText"}, "textMuted": {"dark": "darkSubtext1", "light": "lightSubtext1"}, "background": {"dark": "darkBase", "light": "lightBase"}, "backgroundPanel": {"dark": "darkM<PERSON>le", "light": "lightMantle"}, "backgroundElement": {"dark": "darkCrust", "light": "lightCrust"}, "border": {"dark": "darkSurface0", "light": "lightSurface0"}, "borderActive": {"dark": "darkSurface1", "light": "lightSurface1"}, "borderSubtle": {"dark": "darkSurface2", "light": "lightSurface2"}, "diffAdded": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGreen"}, "diffRemoved": {"dark": "darkRed", "light": "lightRed"}, "diffContext": {"dark": "darkOverlay2", "light": "lightOverlay2"}, "diffHunkHeader": {"dark": "darkPeach", "light": "lightPeach"}, "diffHighlightAdded": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGreen"}, "diffHighlightRemoved": {"dark": "darkRed", "light": "lightRed"}, "diffAddedBg": {"dark": "#24312b", "light": "#d6f0d9"}, "diffRemovedBg": {"dark": "#3c2a32", "light": "#f6dfe2"}, "diffContextBg": {"dark": "darkM<PERSON>le", "light": "lightMantle"}, "diffLineNumber": {"dark": "darkSurface1", "light": "lightSurface1"}, "diffAddedLineNumberBg": {"dark": "#1e2a25", "light": "#c9e3cb"}, "diffRemovedLineNumberBg": {"dark": "#32232a", "light": "#e9d3d6"}, "markdownText": {"dark": "darkText", "light": "lightText"}, "markdownHeading": {"dark": "darkMauve", "light": "lightMauve"}, "markdownLink": {"dark": "darkBlue", "light": "lightBlue"}, "markdownLinkText": {"dark": "darkSky", "light": "lightSky"}, "markdownCode": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGreen"}, "markdownBlockQuote": {"dark": "<PERSON><PERSON><PERSON><PERSON>", "light": "light<PERSON>ellow"}, "markdownEmph": {"dark": "<PERSON><PERSON><PERSON><PERSON>", "light": "light<PERSON>ellow"}, "markdownStrong": {"dark": "darkPeach", "light": "lightPeach"}, "markdownHorizontalRule": {"dark": "darkSubtext0", "light": "lightSubtext0"}, "markdownListItem": {"dark": "darkBlue", "light": "lightBlue"}, "markdownListEnumeration": {"dark": "darkSky", "light": "lightSky"}, "markdownImage": {"dark": "darkBlue", "light": "lightBlue"}, "markdownImageText": {"dark": "darkSky", "light": "lightSky"}, "markdownCodeBlock": {"dark": "darkText", "light": "lightText"}, "syntaxComment": {"dark": "darkOverlay2", "light": "lightOverlay2"}, "syntaxKeyword": {"dark": "darkMauve", "light": "lightMauve"}, "syntaxFunction": {"dark": "darkBlue", "light": "lightBlue"}, "syntaxVariable": {"dark": "darkRed", "light": "lightRed"}, "syntaxString": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGreen"}, "syntaxNumber": {"dark": "darkPeach", "light": "lightPeach"}, "syntaxType": {"dark": "<PERSON><PERSON><PERSON><PERSON>", "light": "light<PERSON>ellow"}, "syntaxOperator": {"dark": "darkSky", "light": "lightSky"}, "syntaxPunctuation": {"dark": "darkText", "light": "lightText"}}}