{"$schema": "https://opencode.ai/theme.json", "defs": {"sumiInk0": "#1F1F28", "sumiInk1": "#2A2A37", "sumiInk2": "#363646", "sumiInk3": "#54546D", "fujiWhite": "#DCD7BA", "oldWhite": "#C8C093", "fujiGray": "#727169", "oniViolet": "#957FB8", "crystalBlue": "#7E9CD8", "carpYellow": "#C38D9D", "sakuraPink": "#D27E99", "waveAqua": "#76946A", "roninYellow": "#D7A657", "dragonRed": "#E82424", "lotusGreen": "#98BB6C", "waveBlue": "#2D4F67", "lightBg": "#F2E9DE", "lightPaper": "#EAE4D7", "lightText": "#54433A", "lightGray": "#9E9389"}, "theme": {"primary": {"dark": "crystalBlue", "light": "waveBlue"}, "secondary": {"dark": "oniViolet", "light": "oniViolet"}, "accent": {"dark": "sakuraPink", "light": "sakuraPink"}, "error": {"dark": "dragonRed", "light": "dragonRed"}, "warning": {"dark": "r<PERSON><PERSON><PERSON><PERSON>", "light": "r<PERSON><PERSON><PERSON><PERSON>"}, "success": {"dark": "lotusGreen", "light": "lotusGreen"}, "info": {"dark": "waveAqua", "light": "waveAqua"}, "text": {"dark": "fuji<PERSON><PERSON><PERSON>", "light": "lightText"}, "textMuted": {"dark": "f<PERSON>ji<PERSON><PERSON>", "light": "lightGray"}, "background": {"dark": "sumiInk0", "light": "lightBg"}, "backgroundPanel": {"dark": "sumiInk1", "light": "lightPaper"}, "backgroundElement": {"dark": "sumiInk2", "light": "#E3DCD2"}, "border": {"dark": "sumiInk3", "light": "#D4CBBF"}, "borderActive": {"dark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "borderSubtle": {"dark": "sumiInk2", "light": "#DCD4C9"}, "diffAdded": {"dark": "lotusGreen", "light": "lotusGreen"}, "diffRemoved": {"dark": "dragonRed", "light": "dragonRed"}, "diffContext": {"dark": "f<PERSON>ji<PERSON><PERSON>", "light": "lightGray"}, "diffHunkHeader": {"dark": "waveBlue", "light": "waveBlue"}, "diffHighlightAdded": {"dark": "#A9D977", "light": "#89AF5B"}, "diffHighlightRemoved": {"dark": "#F24A4A", "light": "#D61F1F"}, "diffAddedBg": {"dark": "#252E25", "light": "#EAF3E4"}, "diffRemovedBg": {"dark": "#362020", "light": "#FBE6E6"}, "diffContextBg": {"dark": "sumiInk1", "light": "lightPaper"}, "diffLineNumber": {"dark": "sumiInk3", "light": "#C7BEB4"}, "diffAddedLineNumberBg": {"dark": "#202820", "light": "#DDE8D6"}, "diffRemovedLineNumberBg": {"dark": "#2D1C1C", "light": "#F2DADA"}, "markdownText": {"dark": "fuji<PERSON><PERSON><PERSON>", "light": "lightText"}, "markdownHeading": {"dark": "oniViolet", "light": "oniViolet"}, "markdownLink": {"dark": "crystalBlue", "light": "waveBlue"}, "markdownLinkText": {"dark": "waveAqua", "light": "waveAqua"}, "markdownCode": {"dark": "lotusGreen", "light": "lotusGreen"}, "markdownBlockQuote": {"dark": "f<PERSON>ji<PERSON><PERSON>", "light": "lightGray"}, "markdownEmph": {"dark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "markdownStrong": {"dark": "r<PERSON><PERSON><PERSON><PERSON>", "light": "r<PERSON><PERSON><PERSON><PERSON>"}, "markdownHorizontalRule": {"dark": "f<PERSON>ji<PERSON><PERSON>", "light": "lightGray"}, "markdownListItem": {"dark": "crystalBlue", "light": "waveBlue"}, "markdownListEnumeration": {"dark": "waveAqua", "light": "waveAqua"}, "markdownImage": {"dark": "crystalBlue", "light": "waveBlue"}, "markdownImageText": {"dark": "waveAqua", "light": "waveAqua"}, "markdownCodeBlock": {"dark": "fuji<PERSON><PERSON><PERSON>", "light": "lightText"}, "syntaxComment": {"dark": "f<PERSON>ji<PERSON><PERSON>", "light": "lightGray"}, "syntaxKeyword": {"dark": "oniViolet", "light": "oniViolet"}, "syntaxFunction": {"dark": "crystalBlue", "light": "waveBlue"}, "syntaxVariable": {"dark": "fuji<PERSON><PERSON><PERSON>", "light": "lightText"}, "syntaxString": {"dark": "lotusGreen", "light": "lotusGreen"}, "syntaxNumber": {"dark": "r<PERSON><PERSON><PERSON><PERSON>", "light": "r<PERSON><PERSON><PERSON><PERSON>"}, "syntaxType": {"dark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "syntaxOperator": {"dark": "sakuraPink", "light": "sakuraPink"}, "syntaxPunctuation": {"dark": "fuji<PERSON><PERSON><PERSON>", "light": "lightText"}}}