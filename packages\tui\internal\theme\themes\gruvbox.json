{"$schema": "https://opencode.ai/theme.json", "defs": {"darkBg0": "#282828", "darkBg1": "#3c3836", "darkBg2": "#504945", "darkBg3": "#665c54", "darkFg0": "#fbf1c7", "darkFg1": "#ebdbb2", "darkGray": "#928374", "darkRed": "#cc241d", "darkGreen": "#98971a", "darkYellow": "#d79921", "darkBlue": "#458588", "darkPurple": "#b16286", "darkAqua": "#689d6a", "darkOrange": "#d65d0e", "darkRedBright": "#fb4934", "darkGreenBright": "#b8bb26", "darkYellowBright": "#fabd2f", "darkBlueBright": "#83a598", "darkPurpleBright": "#d3869b", "darkAquaBright": "#8ec07c", "darkOrangeBright": "#fe8019", "lightBg0": "#fbf1c7", "lightBg1": "#ebdbb2", "lightBg2": "#d5c4a1", "lightBg3": "#bdae93", "lightFg0": "#282828", "lightFg1": "#3c3836", "lightGray": "#7c6f64", "lightRed": "#9d0006", "lightGreen": "#79740e", "lightYellow": "#b57614", "lightBlue": "#076678", "lightPurple": "#8f3f71", "lightAqua": "#427b58", "lightOrange": "#af3a03"}, "theme": {"primary": {"dark": "darkBlueBright", "light": "lightBlue"}, "secondary": {"dark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "lightP<PERSON>ple"}, "accent": {"dark": "dark<PERSON><PERSON><PERSON><PERSON>", "light": "lightAqua"}, "error": {"dark": "darkRed<PERSON>right", "light": "lightRed"}, "warning": {"dark": "dark<PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "lightOrange"}, "success": {"dark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "lightGreen"}, "info": {"dark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "light<PERSON>ellow"}, "text": {"dark": "darkFg1", "light": "lightFg1"}, "textMuted": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGray"}, "background": {"dark": "darkBg0", "light": "lightBg0"}, "backgroundPanel": {"dark": "darkBg1", "light": "lightBg1"}, "backgroundElement": {"dark": "darkBg2", "light": "lightBg2"}, "border": {"dark": "darkBg3", "light": "lightBg3"}, "borderActive": {"dark": "darkFg1", "light": "lightFg1"}, "borderSubtle": {"dark": "darkBg2", "light": "lightBg2"}, "diffAdded": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGreen"}, "diffRemoved": {"dark": "darkRed", "light": "lightRed"}, "diffContext": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGray"}, "diffHunkHeader": {"dark": "darkAqua", "light": "lightAqua"}, "diffHighlightAdded": {"dark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "lightGreen"}, "diffHighlightRemoved": {"dark": "darkRed<PERSON>right", "light": "lightRed"}, "diffAddedBg": {"dark": "#32302f", "light": "#e2e0b5"}, "diffRemovedBg": {"dark": "#322929", "light": "#e9d8d5"}, "diffContextBg": {"dark": "darkBg1", "light": "lightBg1"}, "diffLineNumber": {"dark": "darkBg3", "light": "lightBg3"}, "diffAddedLineNumberBg": {"dark": "#2a2827", "light": "#d4d2a9"}, "diffRemovedLineNumberBg": {"dark": "#2a2222", "light": "#d8cbc8"}, "markdownText": {"dark": "darkFg1", "light": "lightFg1"}, "markdownHeading": {"dark": "darkBlueBright", "light": "lightBlue"}, "markdownLink": {"dark": "dark<PERSON><PERSON><PERSON><PERSON>", "light": "lightAqua"}, "markdownLinkText": {"dark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "lightGreen"}, "markdownCode": {"dark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "light<PERSON>ellow"}, "markdownBlockQuote": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGray"}, "markdownEmph": {"dark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "lightP<PERSON>ple"}, "markdownStrong": {"dark": "dark<PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "lightOrange"}, "markdownHorizontalRule": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGray"}, "markdownListItem": {"dark": "darkBlueBright", "light": "lightBlue"}, "markdownListEnumeration": {"dark": "dark<PERSON><PERSON><PERSON><PERSON>", "light": "lightAqua"}, "markdownImage": {"dark": "dark<PERSON><PERSON><PERSON><PERSON>", "light": "lightAqua"}, "markdownImageText": {"dark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "lightGreen"}, "markdownCodeBlock": {"dark": "darkFg1", "light": "lightFg1"}, "syntaxComment": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGray"}, "syntaxKeyword": {"dark": "darkRed<PERSON>right", "light": "lightRed"}, "syntaxFunction": {"dark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "lightGreen"}, "syntaxVariable": {"dark": "darkBlueBright", "light": "lightBlue"}, "syntaxString": {"dark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "light<PERSON>ellow"}, "syntaxNumber": {"dark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "lightP<PERSON>ple"}, "syntaxType": {"dark": "dark<PERSON><PERSON><PERSON><PERSON>", "light": "lightAqua"}, "syntaxOperator": {"dark": "dark<PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "lightOrange"}, "syntaxPunctuation": {"dark": "darkFg1", "light": "lightFg1"}}}