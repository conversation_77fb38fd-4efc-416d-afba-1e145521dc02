---
title: Commands
description: Create custom commands for repetitive tasks.
---

Custom commands let you specify a prompt you want to run when that command is executed in the TUI.

```bash frame="none"
/my-command
```

Custom commands are in addition to the built-in commands like `/init`, `/undo`, `/redo`, `/share`, `/help`. [Learn more](/docs/tui#commands).

---

## Create command files

Create markdown files in the `command/` directory to define custom commands.

Create `.opencode/command/test.md`:

```md title=".opencode/command/test.md"
---
description: Run tests with coverage
agent: build
model: anthropic/claude-3-5-sonnet-20241022
---

Run the full test suite with coverage report and show any failures.
Focus on the failing tests and suggest fixes.
```

The frontmatter defines command properties. The content becomes the template.

Use the command by typing `/` followed by the command name.

```bash frame="none"
"/test"
```

---

## Configure

You can add custom commands through the opencode config or by creating markdown files in the `command/` directory.

---

### JSON

Use the `command` option in your opencode [config](/docs/config):

```json title="opencode.jsonc" {4-12}
{
  "$schema": "https://opencode.ai/config.json",
  "command": {
    // This becomes the name of the command
    "test": {
      // This is the prompt that will be sent to the LLM
      "template": "Run the full test suite with coverage report and show any failures.\nFocus on the failing tests and suggest fixes.",
      // This is show as the description in the TUI
      "description": "Run tests with coverage",
      "agent": "build",
      "model": "anthropic/claude-3-5-sonnet-20241022"
    },
  }
}
```

Now you can run this command in the TUI:

```bash frame="none"
/test
```

---

### Markdown

You can also define commands using markdown files. Place them in:

- Global: `~/.config/opencode/command/`
- Per-project: `.opencode/command/`

```markdown title="~/.config/opencode/command/test.md"
---
description: Run tests with coverage
agent: build
model: anthropic/claude-3-5-sonnet-20241022
---

Run the full test suite with coverage report and show any failures.
Focus on the failing tests and suggest fixes.
```

The markdown file name becomes the command name. For example, `test.md` lets
you run:

```bash frame="none"
/test
```

---

## Prompt config

The prompts for the custom commands support several special placeholders and syntax.

---

### Arguments

Pass arguments to commands using the `$ARGUMENTS` placeholder.

```md title=".opencode/command/component.md"
---
description: Create a new component
---

Create a new React component named $ARGUMENTS with TypeScript support.
Include proper typing and basic structure.
```

Run the command with arguments:

```bash frame="none"
/component Button
```

And `$ARGUMENTS` will be replaced with `Button`.

---

### Shell output

Use _!`command`_ to inject [bash command](/docs/tui#bash-commands) output into your prompt.

For example, to create a custom command that analyzes test coverage:

```md title=".opencode/command/analyze-coverage.md"
---
description: Analyze test coverage
---

Here are the current test results:
!`npm test`

Based on these results, suggest improvements to increase coverage.
```

Or to review recent changes:

```md title=".opencode/command/review-changes.md"
---
description: Review recent changes
---

Recent git commits:
!`git log --oneline -10`

Review these changes and suggest any improvements.
```

Commands run in your project's root directory and their output becomes part of the prompt.

---

### File references

Include files in your command using `@` followed by the filename.

```md title=".opencode/command/review-component.md"
---
description: Review component
---

Review the component in @src/components/Button.tsx.
Check for performance issues and suggest improvements.
```

The file content gets included in the prompt automatically.

---

## Options

Let's look at the configuration options in detail.

---

### Template

The `template` option defines the prompt that will be sent to the LLM when the command is executed.

```json title="opencode.json"
{
  "command": {
    "test": {
      "template": "Run the full test suite with coverage report and show any failures.\nFocus on the failing tests and suggest fixes."
    }
  }
}
```

This is a **required** config option.

---

### Description

Use the `description` option to provide a brief description of what the command does.

```json title="opencode.json"
{
  "command": {
    "test": {
      "description": "Run tests with coverage"
    }
  }
}
```

This is shown as the description in the TUI when you type in the command.

---

### Agent

Use the `agent` config to optionally specify which [agent](/docs/agents) should execute this command.

```json title="opencode.json"
{
  "command": {
    "review": {
      "agent": "plan"
    }
  }
}
```

This is an **optional** config option. If not specified, defaults to "build".

---

### Model

Use the `model` config to override the default model for this command.

```json title="opencode.json"
{
  "command": {
    "analyze": {
      "model": "anthropic/claude-3-5-sonnet-20241022"
    }
  }
}
```

This is an **optional** config option.

---

## Built-in

opencode includes several built-in commands like `/init`, `/undo`, `/redo`, `/share`, `/help`; [learn more](/docs/tui#commands).

:::note
Custom commands can override built-in commands.
:::

If you define a custom command with the same name, it will override the built-in command.
