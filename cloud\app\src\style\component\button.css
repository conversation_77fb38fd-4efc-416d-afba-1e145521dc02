[data-component="button"] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border: 1px solid transparent;
  border-radius: var(--space-2);
  font-family: var(--font-sans);
  font-size: var(--font-size-md);
  font-weight: 500;
  line-height: 1.25;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
  user-select: none;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--color-primary);
  }

  &[data-color="primary"] {
    background-color: var(--color-primary);
    color: var(--color-primary-text);
    border-color: var(--color-primary);

    &:hover:not(:disabled) {
      background-color: var(--color-primary-hover);
      border-color: var(--color-primary-hover);
    }

    &:active:not(:disabled) {
      background-color: var(--color-primary-active);
      border-color: var(--color-primary-active);
    }
  }

  &[data-color="danger"] {
    background-color: var(--color-danger);
    color: var(--color-danger-text);
    border-color: var(--color-danger);

    &:hover:not(:disabled) {
      background-color: var(--color-danger-hover);
      border-color: var(--color-danger-hover);
    }

    &:active:not(:disabled) {
      background-color: var(--color-danger-active);
      border-color: var(--color-danger-active);
    }

    &:focus {
      box-shadow: 0 0 0 2px var(--color-danger);
    }
  }

  &[data-color="warning"] {
    background-color: var(--color-warning);
    color: var(--color-warning-text);
    border-color: var(--color-warning);

    &:hover:not(:disabled) {
      background-color: var(--color-warning-hover);
      border-color: var(--color-warning-hover);
    }

    &:active:not(:disabled) {
      background-color: var(--color-warning-active);
      border-color: var(--color-warning-active);
    }

    &:focus {
      box-shadow: 0 0 0 2px var(--color-warning);
    }
  }

  &[data-size="small"] {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-sm);
    gap: var(--space-1-5);
  }

  &[data-size="large"] {
    padding: var(--space-4) var(--space-6);
    font-size: var(--font-size-lg);
    gap: var(--space-3);
  }

  [data-slot="icon"] {
    display: flex;
    align-items: center;
    width: 1em;
    height: 1em;
  }
}
