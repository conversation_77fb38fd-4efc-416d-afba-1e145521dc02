---
title: GitLab
description: Use opencode in GitLab issues and merge requests.
---

opencode integrates with your GitLab workflow.
Mention `@opencode` in a comment, and opencode will execute tasks within your GitLab CI pipeline.

---

## Features

- **Triage issues**: Ask opencode to look into an issue and explain it to you.
- **Fix and implement**: Ask opencode to fix an issue or implement a feature.
  It will work create a new branch and raised a merge request with the changes.
- **Secure**: opencode runs on your GitLab runners.

---

## Setup

opencode runs in your GitLab CI/CD pipeline, here's what you'll need to set it up:

:::tip
Check out the [**GitLab docs**](https://docs.gitlab.com/user/duo_agent_platform/agent_assistant/) for up to date instructions.
:::

1. Configure your GitLab environment
2. Set up CI/CD
3. Get an AI model provider API key
4. Create a service account
5. Configure CI/CD variables
6. Create a flow config file, here's an example:

   <details>
     <summary>Flow configuration</summary>

   ```yaml
   image: node:22-slim
   commands:
     - echo "Installing opencode"
     - npm install --global opencode-ai
     - echo "Installing glab"
     - export GITLAB_TOKEN=$GITLAB_TOKEN_OPENCODE
     - apt-get update --quiet && apt-get install --yes curl wget gpg git && rm --recursive --force /var/lib/apt/lists/*
     - curl --silent --show-error --location "https://raw.githubusercontent.com/upciti/wakemeops/main/assets/install_repository" | bash
     - apt-get install --yes glab
     - echo "Configuring glab"
     - echo $GITLAB_HOST
     - echo "Creating opencode auth configuration"
     - mkdir --parents ~/.local/share/opencode
     - |
       cat > ~/.local/share/opencode/auth.json << EOF
       {
         "anthropic": {
           "type": "api",
           "key": "$ANTHROPIC_API_KEY"
         }
       }
       EOF
     - echo "Configuring git"
     - git config --global user.email "<EMAIL>"
     - git config --global user.name "Opencode"
     - echo "Testing glab"
     - glab issue list
     - echo "Running Opencode"
     - |
       opencode run "
       You are an AI assistant helping with GitLab operations.
   
       Context: $AI_FLOW_CONTEXT
       Task: $AI_FLOW_INPUT
       Event: $AI_FLOW_EVENT
   
       Please execute the requested task using the available GitLab tools.
       Be thorough in your analysis and provide clear explanations.
   
       <important>
       Please use the glab CLI to access data from GitLab. The glab CLI has already been authenticated. You can run the corresponding commands.
   
       If you are asked to summarise an MR or issue or asked to provide more information then please post back a note to the MR/Issue so that the user can see it.
       You don't need to commit or push up changes, those will be done automatically based on the file changes you make.
       </important>
       "
     - git checkout --branch $CI_WORKLOAD_REF origin/$CI_WORKLOAD_REF
     - echo "Checking for git changes and pushing if any exist"
     - |
       if ! git diff --quiet || ! git diff --cached --quiet || [ --not --zero "$(git ls-files --others --exclude-standard)" ]; then
         echo "Git changes detected, adding and pushing..."
         git add .
         if git diff --cached --quiet; then
           echo "No staged changes to commit"
         else
           echo "Committing changes to branch: $CI_WORKLOAD_REF"
           git commit --message "Codex changes"
           echo "Pushing changes up to $CI_WORKLOAD_REF"
           git push https://gitlab-ci-token:$GITLAB_TOKEN@$GITLAB_HOST/gl-demo-ultimate-dev-ai-epic-17570/test-java-project.git $CI_WORKLOAD_REF
           echo "Changes successfully pushed"
         fi
       else
         echo "No git changes detected, skipping push"
       fi
   variables:
     - ANTHROPIC_API_KEY
     - GITLAB_TOKEN_OPENCODE
     - GITLAB_HOST
   ```

   </details>

You can refer to the [GitLab CLI agents docs](https://docs.gitlab.com/user/duo_agent_platform/agent_assistant/) for detailed instructions.

---

## Examples

Here are some examples of how you can use opencode in GitLab.

:::tip
You can configure to use a different trigger phrase than `@opencode`.
:::

- **Explain an issue**

  Add this comment in a GitLab issue.

  ```
  @opencode explain this issue
  ```

  opencode will read the issue and reply with a clear explanation.

- **Fix an issue**

  In a GitLab issue, say:

  ```
  @opencode fix this
  ```

  opencode will create a new branch, implement the changes, and open a merge request with the changes.

- **Review merge requests**

  Leave the following comment on a GitLab merge request.

  ```
  @opencode review this merge request
  ```

  opencode will review the merge request and provide feedback.
