{"name": "@opencode/web", "type": "module", "version": "0.6.3", "scripts": {"dev": "astro dev", "dev:remote": "sst shell --stage=dev --target=Web astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@astrojs/cloudflare": "12.6.3", "@astrojs/markdown-remark": "6.3.1", "@astrojs/solid-js": "5.1.0", "@astrojs/starlight": "0.34.3", "@fontsource/ibm-plex-mono": "5.2.5", "@shikijs/transformers": "3.4.2", "@types/luxon": "3.6.2", "ai": "catalog:", "astro": "5.7.13", "diff": "8.0.2", "js-base64": "3.7.7", "lang-map": "0.4.0", "luxon": "3.6.1", "marked": "15.0.12", "marked-shiki": "1.2.1", "rehype-autolink-headings": "7.1.0", "remeda": "2.26.0", "sharp": "0.32.5", "shiki": "3.4.2", "solid-js": "catalog:", "toolbeam-docs-theme": "0.4.6"}, "devDependencies": {"opencode": "workspace:*", "@types/node": "catalog:", "typescript": "catalog:"}}