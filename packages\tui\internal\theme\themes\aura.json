{"$schema": "https://opencode.ai/theme.json", "defs": {"darkBg": "#0f0f0f", "darkBgPanel": "#15141b", "darkBorder": "#2d2d2d", "darkFgMuted": "#6d6d6d", "darkFg": "#edecee", "purple": "#a277ff", "pink": "#f694ff", "blue": "#82e2ff", "red": "#ff6767", "orange": "#ffca85", "cyan": "#61ffca", "green": "#9dff65"}, "theme": {"primary": "purple", "secondary": "pink", "accent": "purple", "error": "red", "warning": "orange", "success": "cyan", "info": "purple", "text": "darkFg", "textMuted": "darkFgMuted", "background": "darkBg", "backgroundPanel": "darkBgPanel", "backgroundElement": "darkBgPanel", "border": "darkBorder", "borderActive": "darkFgMuted", "borderSubtle": "darkBorder", "diffAdded": "cyan", "diffRemoved": "red", "diffContext": "darkFgMuted", "diffHunkHeader": "darkFgMuted", "diffHighlightAdded": "cyan", "diffHighlightRemoved": "red", "diffAddedBg": "#354933", "diffRemovedBg": "#3f191a", "diffContextBg": "darkBgPanel", "diffLineNumber": "darkBorder", "diffAddedLineNumberBg": "#162620", "diffRemovedLineNumberBg": "#26161a", "markdownText": "darkFg", "markdownHeading": "purple", "markdownLink": "pink", "markdownLinkText": "purple", "markdownCode": "cyan", "markdownBlockQuote": "darkFgMuted", "markdownEmph": "orange", "markdownStrong": "purple", "markdownHorizontalRule": "darkFgMuted", "markdownListItem": "purple", "markdownListEnumeration": "purple", "markdownImage": "pink", "markdownImageText": "purple", "markdownCodeBlock": "darkFg", "syntaxComment": "darkFgMuted", "syntaxKeyword": "pink", "syntaxFunction": "purple", "syntaxVariable": "purple", "syntaxString": "cyan", "syntaxNumber": "green", "syntaxType": "purple", "syntaxOperator": "pink", "syntaxPunctuation": "darkFg"}}