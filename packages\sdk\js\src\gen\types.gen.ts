// This file is auto-generated by @hey-api/openapi-ts

export type Project = {
  id: string
  worktree: string
  vcs?: "git"
  time: {
    created: number
    initialized?: number
  }
}

export type Event =
  | ({
      type: "installation.updated"
    } & EventInstallationUpdated)
  | ({
      type: "lsp.client.diagnostics"
    } & EventLspClientDiagnostics)
  | ({
      type: "message.updated"
    } & EventMessageUpdated)
  | ({
      type: "message.removed"
    } & EventMessageRemoved)
  | ({
      type: "message.part.updated"
    } & EventMessagePartUpdated)
  | ({
      type: "message.part.removed"
    } & EventMessagePartRemoved)
  | ({
      type: "permission.updated"
    } & EventPermissionUpdated)
  | ({
      type: "permission.replied"
    } & EventPermissionReplied)
  | ({
      type: "file.edited"
    } & EventFileEdited)
  | ({
      type: "session.updated"
    } & EventSessionUpdated)
  | ({
      type: "session.deleted"
    } & EventSessionDeleted)
  | ({
      type: "session.idle"
    } & EventSessionIdle)
  | ({
      type: "session.error"
    } & EventSessionError)
  | ({
      type: "server.connected"
    } & EventServerConnected)

export type EventInstallationUpdated = {
  type: "installation.updated"
  properties: {
    version: string
  }
}

export type EventLspClientDiagnostics = {
  type: "lsp.client.diagnostics"
  properties: {
    serverID: string
    path: string
  }
}

export type EventMessageUpdated = {
  type: "message.updated"
  properties: {
    info: Message
  }
}

export type Message =
  | ({
      role: "user"
    } & UserMessage)
  | ({
      role: "assistant"
    } & AssistantMessage)

export type UserMessage = {
  id: string
  sessionID: string
  role: "user"
  time: {
    created: number
  }
}

export type AssistantMessage = {
  id: string
  sessionID: string
  role: "assistant"
  time: {
    created: number
    completed?: number
  }
  error?:
    | ({
        name: "ProviderAuthError"
      } & ProviderAuthError)
    | ({
        name: "UnknownError"
      } & UnknownError)
    | ({
        name: "MessageOutputLengthError"
      } & MessageOutputLengthError)
    | ({
        name: "MessageAbortedError"
      } & MessageAbortedError)
  system: Array<string>
  modelID: string
  providerID: string
  mode: string
  path: {
    cwd: string
    root: string
  }
  summary?: boolean
  cost: number
  tokens: {
    input: number
    output: number
    reasoning: number
    cache: {
      read: number
      write: number
    }
  }
}

export type ProviderAuthError = {
  name: "ProviderAuthError"
  data: {
    providerID: string
    message: string
  }
}

export type UnknownError = {
  name: "UnknownError"
  data: {
    message: string
  }
}

export type MessageOutputLengthError = {
  name: "MessageOutputLengthError"
  data: {
    [key: string]: unknown
  }
}

export type MessageAbortedError = {
  name: "MessageAbortedError"
  data: {
    [key: string]: unknown
  }
}

export type EventMessageRemoved = {
  type: "message.removed"
  properties: {
    sessionID: string
    messageID: string
  }
}

export type EventMessagePartUpdated = {
  type: "message.part.updated"
  properties: {
    part: Part
  }
}

export type Part =
  | ({
      type: "text"
    } & TextPart)
  | ({
      type: "reasoning"
    } & ReasoningPart)
  | ({
      type: "file"
    } & FilePart)
  | ({
      type: "tool"
    } & ToolPart)
  | ({
      type: "step-start"
    } & StepStartPart)
  | ({
      type: "step-finish"
    } & StepFinishPart)
  | ({
      type: "snapshot"
    } & SnapshotPart)
  | ({
      type: "patch"
    } & PatchPart)
  | ({
      type: "agent"
    } & AgentPart)

export type TextPart = {
  id: string
  sessionID: string
  messageID: string
  type: "text"
  text: string
  synthetic?: boolean
  time?: {
    start: number
    end?: number
  }
}

export type ReasoningPart = {
  id: string
  sessionID: string
  messageID: string
  type: "reasoning"
  text: string
  metadata?: {
    [key: string]: unknown
  }
  time: {
    start: number
    end?: number
  }
}

export type FilePart = {
  id: string
  sessionID: string
  messageID: string
  type: "file"
  mime: string
  filename?: string
  url: string
  source?: FilePartSource
}

export type FilePartSource =
  | ({
      type: "file"
    } & FileSource)
  | ({
      type: "symbol"
    } & SymbolSource)

export type FileSource = {
  text: FilePartSourceText
  type: "file"
  path: string
}

export type FilePartSourceText = {
  value: string
  start: number
  end: number
}

export type SymbolSource = {
  text: FilePartSourceText
  type: "symbol"
  path: string
  range: Range
  name: string
  kind: number
}

export type Range = {
  start: {
    line: number
    character: number
  }
  end: {
    line: number
    character: number
  }
}

export type ToolPart = {
  id: string
  sessionID: string
  messageID: string
  type: "tool"
  callID: string
  tool: string
  state: ToolState
}

export type ToolState =
  | ({
      status: "pending"
    } & ToolStatePending)
  | ({
      status: "running"
    } & ToolStateRunning)
  | ({
      status: "completed"
    } & ToolStateCompleted)
  | ({
      status: "error"
    } & ToolStateError)

export type ToolStatePending = {
  status: "pending"
}

export type ToolStateRunning = {
  status: "running"
  input?: unknown
  title?: string
  metadata?: {
    [key: string]: unknown
  }
  time: {
    start: number
  }
}

export type ToolStateCompleted = {
  status: "completed"
  input: {
    [key: string]: unknown
  }
  output: string
  title: string
  metadata: {
    [key: string]: unknown
  }
  time: {
    start: number
    end: number
  }
}

export type ToolStateError = {
  status: "error"
  input: {
    [key: string]: unknown
  }
  error: string
  metadata?: {
    [key: string]: unknown
  }
  time: {
    start: number
    end: number
  }
}

export type StepStartPart = {
  id: string
  sessionID: string
  messageID: string
  type: "step-start"
}

export type StepFinishPart = {
  id: string
  sessionID: string
  messageID: string
  type: "step-finish"
  cost: number
  tokens: {
    input: number
    output: number
    reasoning: number
    cache: {
      read: number
      write: number
    }
  }
}

export type SnapshotPart = {
  id: string
  sessionID: string
  messageID: string
  type: "snapshot"
  snapshot: string
}

export type PatchPart = {
  id: string
  sessionID: string
  messageID: string
  type: "patch"
  hash: string
  files: Array<string>
}

export type AgentPart = {
  id: string
  sessionID: string
  messageID: string
  type: "agent"
  name: string
  source?: {
    value: string
    start: number
    end: number
  }
}

export type EventMessagePartRemoved = {
  type: "message.part.removed"
  properties: {
    sessionID: string
    messageID: string
    partID: string
  }
}

export type EventPermissionUpdated = {
  type: "permission.updated"
  properties: Permission
}

export type Permission = {
  id: string
  type: string
  pattern?: string
  sessionID: string
  messageID: string
  callID?: string
  title: string
  metadata: {
    [key: string]: unknown
  }
  time: {
    created: number
  }
}

export type EventPermissionReplied = {
  type: "permission.replied"
  properties: {
    sessionID: string
    permissionID: string
    response: string
  }
}

export type EventFileEdited = {
  type: "file.edited"
  properties: {
    file: string
  }
}

export type EventSessionUpdated = {
  type: "session.updated"
  properties: {
    info: Session
  }
}

export type Session = {
  id: string
  projectID: string
  directory: string
  parentID?: string
  share?: {
    url: string
  }
  title: string
  version: string
  time: {
    created: number
    updated: number
  }
  revert?: {
    messageID: string
    partID?: string
    snapshot?: string
    diff?: string
  }
}

export type EventSessionDeleted = {
  type: "session.deleted"
  properties: {
    info: Session
  }
}

export type EventSessionIdle = {
  type: "session.idle"
  properties: {
    sessionID: string
  }
}

export type EventSessionError = {
  type: "session.error"
  properties: {
    sessionID?: string
    error?:
      | ({
          name: "ProviderAuthError"
        } & ProviderAuthError)
      | ({
          name: "UnknownError"
        } & UnknownError)
      | ({
          name: "MessageOutputLengthError"
        } & MessageOutputLengthError)
      | ({
          name: "MessageAbortedError"
        } & MessageAbortedError)
  }
}

export type EventServerConnected = {
  type: "server.connected"
  properties: {
    [key: string]: unknown
  }
}

export type Config = {
  /**
   * JSON schema reference for configuration validation
   */
  $schema?: string
  /**
   * Theme name to use for the interface
   */
  theme?: string
  /**
   * Custom keybind configurations
   */
  keybinds?: KeybindsConfig
  /**
   * TUI specific settings
   */
  tui?: {
    /**
     * TUI scroll speed
     */
    scroll_speed: number
  }
  /**
   * Command configuration, see https://opencode.ai/docs/commands
   */
  command?: {
    [key: string]: {
      template: string
      description?: string
      agent?: string
      model?: string
    }
  }
  plugin?: Array<string>
  snapshot?: boolean
  /**
   * Control sharing behavior:'manual' allows manual sharing via commands, 'auto' enables automatic sharing, 'disabled' disables all sharing
   */
  share?: "manual" | "auto" | "disabled"
  /**
   * @deprecated Use 'share' field instead. Share newly created sessions automatically
   */
  autoshare?: boolean
  /**
   * Automatically update to the latest version
   */
  autoupdate?: boolean
  /**
   * Disable providers that are loaded automatically
   */
  disabled_providers?: Array<string>
  /**
   * Model to use in the format of provider/model, eg anthropic/claude-2
   */
  model?: string
  /**
   * Small model to use for tasks like title generation in the format of provider/model
   */
  small_model?: string
  /**
   * Custom username to display in conversations instead of system username
   */
  username?: string
  /**
   * @deprecated Use `agent` field instead.
   */
  mode?: {
    build?: AgentConfig
    plan?: AgentConfig
    [key: string]: AgentConfig | undefined
  }
  /**
   * Agent configuration, see https://opencode.ai/docs/agent
   */
  agent?: {
    plan?: AgentConfig
    build?: AgentConfig
    general?: AgentConfig
    [key: string]: AgentConfig | undefined
  }
  /**
   * Custom provider configurations and model overrides
   */
  provider?: {
    [key: string]: {
      api?: string
      name?: string
      env?: Array<string>
      id?: string
      npm?: string
      models?: {
        [key: string]: {
          id?: string
          name?: string
          release_date?: string
          attachment?: boolean
          reasoning?: boolean
          temperature?: boolean
          tool_call?: boolean
          cost?: {
            input: number
            output: number
            cache_read?: number
            cache_write?: number
          }
          limit?: {
            context: number
            output: number
          }
          options?: {
            [key: string]: unknown
          }
        }
      }
      options?: {
        apiKey?: string
        baseURL?: string
        /**
         * Timeout in milliseconds for requests to this provider. Default is 300000 (5 minutes). Set to false to disable timeout.
         */
        timeout?: number | false
        [key: string]: unknown | string | (number | false) | undefined
      }
    }
  }
  /**
   * MCP (Model Context Protocol) server configurations
   */
  mcp?: {
    [key: string]:
      | ({
          type: "local"
        } & McpLocalConfig)
      | ({
          type: "remote"
        } & McpRemoteConfig)
  }
  formatter?: {
    [key: string]: {
      disabled?: boolean
      command?: Array<string>
      environment?: {
        [key: string]: string
      }
      extensions?: Array<string>
    }
  }
  lsp?: {
    [key: string]:
      | {
          disabled: true
        }
      | {
          command: Array<string>
          extensions?: Array<string>
          disabled?: boolean
          env?: {
            [key: string]: string
          }
          initialization?: {
            [key: string]: unknown
          }
        }
  }
  /**
   * Additional instruction files or patterns to include
   */
  instructions?: Array<string>
  /**
   * @deprecated Always uses stretch layout.
   */
  layout?: LayoutConfig
  permission?: {
    edit?: "ask" | "allow" | "deny"
    bash?:
      | ("ask" | "allow" | "deny")
      | {
          [key: string]: "ask" | "allow" | "deny"
        }
    webfetch?: "ask" | "allow" | "deny"
  }
  tools?: {
    [key: string]: boolean
  }
  experimental?: {
    hook?: {
      file_edited?: {
        [key: string]: Array<{
          command: Array<string>
          environment?: {
            [key: string]: string
          }
        }>
      }
      session_completed?: Array<{
        command: Array<string>
        environment?: {
          [key: string]: string
        }
      }>
    }
  }
}

export type KeybindsConfig = {
  /**
   * Leader key for keybind combinations
   */
  leader: string
  /**
   * Show help dialog
   */
  app_help: string
  /**
   * Exit the application
   */
  app_exit: string
  /**
   * Open external editor
   */
  editor_open: string
  /**
   * List available themes
   */
  theme_list: string
  /**
   * Create/update AGENTS.md
   */
  project_init: string
  /**
   * Toggle tool details
   */
  tool_details: string
  /**
   * Toggle thinking blocks
   */
  thinking_blocks: string
  /**
   * Export session to editor
   */
  session_export: string
  /**
   * Create a new session
   */
  session_new: string
  /**
   * List all sessions
   */
  session_list: string
  /**
   * Show session timeline
   */
  session_timeline: string
  /**
   * Share current session
   */
  session_share: string
  /**
   * Unshare current session
   */
  session_unshare: string
  /**
   * Interrupt current session
   */
  session_interrupt: string
  /**
   * Compact the session
   */
  session_compact: string
  /**
   * Cycle to next child session
   */
  session_child_cycle: string
  /**
   * Cycle to previous child session
   */
  session_child_cycle_reverse: string
  /**
   * Scroll messages up by one page
   */
  messages_page_up: string
  /**
   * Scroll messages down by one page
   */
  messages_page_down: string
  /**
   * Scroll messages up by half page
   */
  messages_half_page_up: string
  /**
   * Scroll messages down by half page
   */
  messages_half_page_down: string
  /**
   * Navigate to first message
   */
  messages_first: string
  /**
   * Navigate to last message
   */
  messages_last: string
  /**
   * Copy message
   */
  messages_copy: string
  /**
   * Undo message
   */
  messages_undo: string
  /**
   * Redo message
   */
  messages_redo: string
  /**
   * List available models
   */
  model_list: string
  /**
   * Next recent model
   */
  model_cycle_recent: string
  /**
   * Previous recent model
   */
  model_cycle_recent_reverse: string
  /**
   * List agents
   */
  agent_list: string
  /**
   * Next agent
   */
  agent_cycle: string
  /**
   * Previous agent
   */
  agent_cycle_reverse: string
  /**
   * Clear input field
   */
  input_clear: string
  /**
   * Paste from clipboard
   */
  input_paste: string
  /**
   * Submit input
   */
  input_submit: string
  /**
   * Insert newline in input
   */
  input_newline: string
  /**
   * @deprecated use agent_cycle. Next mode
   */
  switch_mode: string
  /**
   * @deprecated use agent_cycle_reverse. Previous mode
   */
  switch_mode_reverse: string
  /**
   * @deprecated use agent_cycle. Next agent
   */
  switch_agent: string
  /**
   * @deprecated use agent_cycle_reverse. Previous agent
   */
  switch_agent_reverse: string
  /**
   * @deprecated Currently not available. List files
   */
  file_list: string
  /**
   * @deprecated Close file
   */
  file_close: string
  /**
   * @deprecated Search file
   */
  file_search: string
  /**
   * @deprecated Split/unified diff
   */
  file_diff_toggle: string
  /**
   * @deprecated Navigate to previous message
   */
  messages_previous: string
  /**
   * @deprecated Navigate to next message
   */
  messages_next: string
  /**
   * @deprecated Toggle layout
   */
  messages_layout_toggle: string
  /**
   * @deprecated use messages_undo. Revert message
   */
  messages_revert: string
}

export type AgentConfig = {
  model?: string
  temperature?: number
  top_p?: number
  prompt?: string
  tools?: {
    [key: string]: boolean
  }
  disable?: boolean
  /**
   * Description of when to use the agent
   */
  description?: string
  mode?: "subagent" | "primary" | "all"
  permission?: {
    edit?: "ask" | "allow" | "deny"
    bash?:
      | ("ask" | "allow" | "deny")
      | {
          [key: string]: "ask" | "allow" | "deny"
        }
    webfetch?: "ask" | "allow" | "deny"
  }
  [key: string]:
    | unknown
    | string
    | number
    | {
        [key: string]: boolean
      }
    | boolean
    | ("subagent" | "primary" | "all")
    | {
        edit?: "ask" | "allow" | "deny"
        bash?:
          | ("ask" | "allow" | "deny")
          | {
              [key: string]: "ask" | "allow" | "deny"
            }
        webfetch?: "ask" | "allow" | "deny"
      }
    | undefined
}

export type Provider = {
  api?: string
  name: string
  env: Array<string>
  id: string
  npm?: string
  models: {
    [key: string]: Model
  }
}

export type Model = {
  id: string
  name: string
  release_date: string
  attachment: boolean
  reasoning: boolean
  temperature: boolean
  tool_call: boolean
  cost: {
    input: number
    output: number
    cache_read?: number
    cache_write?: number
  }
  limit: {
    context: number
    output: number
  }
  options: {
    [key: string]: unknown
  }
}

export type McpLocalConfig = {
  /**
   * Type of MCP server connection
   */
  type: "local"
  /**
   * Command and arguments to run the MCP server
   */
  command: Array<string>
  /**
   * Environment variables to set when running the MCP server
   */
  environment?: {
    [key: string]: string
  }
  /**
   * Enable or disable the MCP server on startup
   */
  enabled?: boolean
}

export type McpRemoteConfig = {
  /**
   * Type of MCP server connection
   */
  type: "remote"
  /**
   * URL of the remote MCP server
   */
  url: string
  /**
   * Enable or disable the MCP server on startup
   */
  enabled?: boolean
  /**
   * Headers to send with the request
   */
  headers?: {
    [key: string]: string
  }
}

export type LayoutConfig = "auto" | "stretch"

export type Path = {
  state: string
  config: string
  worktree: string
  directory: string
}

export type _Error = {
  data: {
    [key: string]: unknown
  }
}

export type TextPartInput = {
  id?: string
  type: "text"
  text: string
  synthetic?: boolean
  time?: {
    start: number
    end?: number
  }
}

export type FilePartInput = {
  id?: string
  type: "file"
  mime: string
  filename?: string
  url: string
  source?: FilePartSource
}

export type AgentPartInput = {
  id?: string
  type: "agent"
  name: string
  source?: {
    value: string
    start: number
    end: number
  }
}

export type Command = {
  name: string
  description?: string
  agent?: string
  model?: string
  template: string
}

export type Symbol = {
  name: string
  kind: number
  location: {
    uri: string
    range: Range
  }
}

export type FileNode = {
  name: string
  path: string
  type: "file" | "directory"
  ignored: boolean
}

export type File = {
  path: string
  added: number
  removed: number
  status: "added" | "deleted" | "modified"
}

export type Agent = {
  name: string
  description?: string
  mode: "subagent" | "primary" | "all"
  builtIn: boolean
  topP?: number
  temperature?: number
  permission: {
    edit: "ask" | "allow" | "deny"
    bash: {
      [key: string]: "ask" | "allow" | "deny"
    }
    webfetch?: "ask" | "allow" | "deny"
  }
  model?: {
    modelID: string
    providerID: string
  }
  prompt?: string
  tools: {
    [key: string]: boolean
  }
  options: {
    [key: string]: unknown
  }
}

export type Auth =
  | ({
      type: "oauth"
    } & OAuth)
  | ({
      type: "api"
    } & ApiAuth)
  | ({
      type: "wellknown"
    } & WellKnownAuth)

export type OAuth = {
  type: "oauth"
  refresh: string
  access: string
  expires: number
}

export type ApiAuth = {
  type: "api"
  key: string
}

export type WellKnownAuth = {
  type: "wellknown"
  key: string
  token: string
}

export type ProjectListData = {
  body?: never
  path?: never
  query?: {
    directory?: string
  }
  url: "/project"
}

export type ProjectListResponses = {
  /**
   * List of projects
   */
  200: Array<Project>
}

export type ProjectListResponse = ProjectListResponses[keyof ProjectListResponses]

export type ProjectCurrentData = {
  body?: never
  path?: never
  query?: {
    directory?: string
  }
  url: "/project/current"
}

export type ProjectCurrentResponses = {
  /**
   * Current project
   */
  200: Project
}

export type ProjectCurrentResponse = ProjectCurrentResponses[keyof ProjectCurrentResponses]

export type EventSubscribeData = {
  body?: never
  path?: never
  query?: {
    directory?: string
  }
  url: "/event"
}

export type EventSubscribeResponses = {
  /**
   * Event stream
   */
  200: Event
}

export type EventSubscribeResponse = EventSubscribeResponses[keyof EventSubscribeResponses]

export type ConfigGetData = {
  body?: never
  path?: never
  query?: {
    directory?: string
  }
  url: "/config"
}

export type ConfigGetResponses = {
  /**
   * Get config info
   */
  200: Config
}

export type ConfigGetResponse = ConfigGetResponses[keyof ConfigGetResponses]

export type PathGetData = {
  body?: never
  path?: never
  query?: {
    directory?: string
  }
  url: "/path"
}

export type PathGetResponses = {
  /**
   * Path
   */
  200: Path
}

export type PathGetResponse = PathGetResponses[keyof PathGetResponses]

export type SessionListData = {
  body?: never
  path?: never
  query?: {
    directory?: string
  }
  url: "/session"
}

export type SessionListResponses = {
  /**
   * List of sessions
   */
  200: Array<Session>
}

export type SessionListResponse = SessionListResponses[keyof SessionListResponses]

export type SessionCreateData = {
  body?: {
    parentID?: string
    title?: string
  }
  path?: never
  query?: {
    directory?: string
  }
  url: "/session"
}

export type SessionCreateErrors = {
  /**
   * Bad request
   */
  400: _Error
}

export type SessionCreateError = SessionCreateErrors[keyof SessionCreateErrors]

export type SessionCreateResponses = {
  /**
   * Successfully created session
   */
  200: Session
}

export type SessionCreateResponse = SessionCreateResponses[keyof SessionCreateResponses]

export type SessionDeleteData = {
  body?: never
  path: {
    id: string
  }
  query?: {
    directory?: string
  }
  url: "/session/{id}"
}

export type SessionDeleteResponses = {
  /**
   * Successfully deleted session
   */
  200: boolean
}

export type SessionDeleteResponse = SessionDeleteResponses[keyof SessionDeleteResponses]

export type SessionGetData = {
  body?: never
  path: {
    id: string
  }
  query?: {
    directory?: string
  }
  url: "/session/{id}"
}

export type SessionGetResponses = {
  /**
   * Get session
   */
  200: Session
}

export type SessionGetResponse = SessionGetResponses[keyof SessionGetResponses]

export type SessionUpdateData = {
  body?: {
    title?: string
  }
  path: {
    id: string
  }
  query?: {
    directory?: string
  }
  url: "/session/{id}"
}

export type SessionUpdateResponses = {
  /**
   * Successfully updated session
   */
  200: Session
}

export type SessionUpdateResponse = SessionUpdateResponses[keyof SessionUpdateResponses]

export type SessionChildrenData = {
  body?: never
  path: {
    id: string
  }
  query?: {
    directory?: string
  }
  url: "/session/{id}/children"
}

export type SessionChildrenResponses = {
  /**
   * List of children
   */
  200: Array<Session>
}

export type SessionChildrenResponse = SessionChildrenResponses[keyof SessionChildrenResponses]

export type SessionInitData = {
  body?: {
    messageID: string
    providerID: string
    modelID: string
  }
  path: {
    /**
     * Session ID
     */
    id: string
  }
  query?: {
    directory?: string
  }
  url: "/session/{id}/init"
}

export type SessionInitResponses = {
  /**
   * 200
   */
  200: boolean
}

export type SessionInitResponse = SessionInitResponses[keyof SessionInitResponses]

export type SessionAbortData = {
  body?: never
  path: {
    id: string
  }
  query?: {
    directory?: string
  }
  url: "/session/{id}/abort"
}

export type SessionAbortResponses = {
  /**
   * Aborted session
   */
  200: boolean
}

export type SessionAbortResponse = SessionAbortResponses[keyof SessionAbortResponses]

export type SessionUnshareData = {
  body?: never
  path: {
    id: string
  }
  query?: {
    directory?: string
  }
  url: "/session/{id}/share"
}

export type SessionUnshareResponses = {
  /**
   * Successfully unshared session
   */
  200: Session
}

export type SessionUnshareResponse = SessionUnshareResponses[keyof SessionUnshareResponses]

export type SessionShareData = {
  body?: never
  path: {
    id: string
  }
  query?: {
    directory?: string
  }
  url: "/session/{id}/share"
}

export type SessionShareResponses = {
  /**
   * Successfully shared session
   */
  200: Session
}

export type SessionShareResponse = SessionShareResponses[keyof SessionShareResponses]

export type SessionSummarizeData = {
  body?: {
    providerID: string
    modelID: string
  }
  path: {
    /**
     * Session ID
     */
    id: string
  }
  query?: {
    directory?: string
  }
  url: "/session/{id}/summarize"
}

export type SessionSummarizeResponses = {
  /**
   * Summarized session
   */
  200: boolean
}

export type SessionSummarizeResponse = SessionSummarizeResponses[keyof SessionSummarizeResponses]

export type SessionMessagesData = {
  body?: never
  path: {
    /**
     * Session ID
     */
    id: string
  }
  query?: {
    directory?: string
  }
  url: "/session/{id}/message"
}

export type SessionMessagesResponses = {
  /**
   * List of messages
   */
  200: Array<{
    info: Message
    parts: Array<Part>
  }>
}

export type SessionMessagesResponse = SessionMessagesResponses[keyof SessionMessagesResponses]

export type SessionPromptData = {
  body?: {
    messageID?: string
    model?: {
      providerID: string
      modelID: string
    }
    agent?: string
    system?: string
    tools?: {
      [key: string]: boolean
    }
    parts: Array<
      | ({
          type: "text"
        } & TextPartInput)
      | ({
          type: "file"
        } & FilePartInput)
      | ({
          type: "agent"
        } & AgentPartInput)
    >
  }
  path: {
    /**
     * Session ID
     */
    id: string
  }
  query?: {
    directory?: string
  }
  url: "/session/{id}/message"
}

export type SessionPromptResponses = {
  /**
   * Created message
   */
  200: {
    info: AssistantMessage
    parts: Array<Part>
  }
}

export type SessionPromptResponse = SessionPromptResponses[keyof SessionPromptResponses]

export type SessionMessageData = {
  body?: never
  path: {
    /**
     * Session ID
     */
    id: string
    /**
     * Message ID
     */
    messageID: string
  }
  query?: {
    directory?: string
  }
  url: "/session/{id}/message/{messageID}"
}

export type SessionMessageResponses = {
  /**
   * Message
   */
  200: {
    info: Message
    parts: Array<Part>
  }
}

export type SessionMessageResponse = SessionMessageResponses[keyof SessionMessageResponses]

export type SessionCommandData = {
  body?: {
    messageID?: string
    agent?: string
    model?: string
    arguments: string
    command: string
  }
  path: {
    /**
     * Session ID
     */
    id: string
  }
  query?: {
    directory?: string
  }
  url: "/session/{id}/command"
}

export type SessionCommandResponses = {
  /**
   * Created message
   */
  200: {
    info: AssistantMessage
    parts: Array<Part>
  }
}

export type SessionCommandResponse = SessionCommandResponses[keyof SessionCommandResponses]

export type SessionShellData = {
  body?: {
    agent: string
    command: string
  }
  path: {
    /**
     * Session ID
     */
    id: string
  }
  query?: {
    directory?: string
  }
  url: "/session/{id}/shell"
}

export type SessionShellResponses = {
  /**
   * Created message
   */
  200: AssistantMessage
}

export type SessionShellResponse = SessionShellResponses[keyof SessionShellResponses]

export type SessionRevertData = {
  body?: {
    messageID: string
    partID?: string
  }
  path: {
    id: string
  }
  query?: {
    directory?: string
  }
  url: "/session/{id}/revert"
}

export type SessionRevertResponses = {
  /**
   * Updated session
   */
  200: Session
}

export type SessionRevertResponse = SessionRevertResponses[keyof SessionRevertResponses]

export type SessionUnrevertData = {
  body?: never
  path: {
    id: string
  }
  query?: {
    directory?: string
  }
  url: "/session/{id}/unrevert"
}

export type SessionUnrevertResponses = {
  /**
   * Updated session
   */
  200: Session
}

export type SessionUnrevertResponse = SessionUnrevertResponses[keyof SessionUnrevertResponses]

export type PostSessionByIdPermissionsByPermissionIdData = {
  body?: {
    response: "once" | "always" | "reject"
  }
  path: {
    id: string
    permissionID: string
  }
  query?: {
    directory?: string
  }
  url: "/session/{id}/permissions/{permissionID}"
}

export type PostSessionByIdPermissionsByPermissionIdResponses = {
  /**
   * Permission processed successfully
   */
  200: boolean
}

export type PostSessionByIdPermissionsByPermissionIdResponse =
  PostSessionByIdPermissionsByPermissionIdResponses[keyof PostSessionByIdPermissionsByPermissionIdResponses]

export type CommandListData = {
  body?: never
  path?: never
  query?: {
    directory?: string
  }
  url: "/command"
}

export type CommandListResponses = {
  /**
   * List of commands
   */
  200: Array<Command>
}

export type CommandListResponse = CommandListResponses[keyof CommandListResponses]

export type ConfigProvidersData = {
  body?: never
  path?: never
  query?: {
    directory?: string
  }
  url: "/config/providers"
}

export type ConfigProvidersResponses = {
  /**
   * List of providers
   */
  200: {
    providers: Array<Provider>
    default: {
      [key: string]: string
    }
  }
}

export type ConfigProvidersResponse = ConfigProvidersResponses[keyof ConfigProvidersResponses]

export type FindTextData = {
  body?: never
  path?: never
  query: {
    directory?: string
    pattern: string
  }
  url: "/find"
}

export type FindTextResponses = {
  /**
   * Matches
   */
  200: Array<{
    path: {
      text: string
    }
    lines: {
      text: string
    }
    line_number: number
    absolute_offset: number
    submatches: Array<{
      match: {
        text: string
      }
      start: number
      end: number
    }>
  }>
}

export type FindTextResponse = FindTextResponses[keyof FindTextResponses]

export type FindFilesData = {
  body?: never
  path?: never
  query: {
    directory?: string
    query: string
  }
  url: "/find/file"
}

export type FindFilesResponses = {
  /**
   * File paths
   */
  200: Array<string>
}

export type FindFilesResponse = FindFilesResponses[keyof FindFilesResponses]

export type FindSymbolsData = {
  body?: never
  path?: never
  query: {
    directory?: string
    query: string
  }
  url: "/find/symbol"
}

export type FindSymbolsResponses = {
  /**
   * Symbols
   */
  200: Array<Symbol>
}

export type FindSymbolsResponse = FindSymbolsResponses[keyof FindSymbolsResponses]

export type FileListData = {
  body?: never
  path?: never
  query: {
    directory?: string
    path: string
  }
  url: "/file"
}

export type FileListResponses = {
  /**
   * Files and directories
   */
  200: Array<FileNode>
}

export type FileListResponse = FileListResponses[keyof FileListResponses]

export type FileReadData = {
  body?: never
  path?: never
  query: {
    directory?: string
    path: string
  }
  url: "/file/content"
}

export type FileReadResponses = {
  /**
   * File content
   */
  200: {
    type: "raw" | "patch"
    content: string
  }
}

export type FileReadResponse = FileReadResponses[keyof FileReadResponses]

export type FileStatusData = {
  body?: never
  path?: never
  query?: {
    directory?: string
  }
  url: "/file/status"
}

export type FileStatusResponses = {
  /**
   * File status
   */
  200: Array<File>
}

export type FileStatusResponse = FileStatusResponses[keyof FileStatusResponses]

export type AppLogData = {
  body?: {
    /**
     * Service name for the log entry
     */
    service: string
    /**
     * Log level
     */
    level: "debug" | "info" | "error" | "warn"
    /**
     * Log message
     */
    message: string
    /**
     * Additional metadata for the log entry
     */
    extra?: {
      [key: string]: unknown
    }
  }
  path?: never
  query?: {
    directory?: string
  }
  url: "/log"
}

export type AppLogResponses = {
  /**
   * Log entry written successfully
   */
  200: boolean
}

export type AppLogResponse = AppLogResponses[keyof AppLogResponses]

export type AppAgentsData = {
  body?: never
  path?: never
  query?: {
    directory?: string
  }
  url: "/agent"
}

export type AppAgentsResponses = {
  /**
   * List of agents
   */
  200: Array<Agent>
}

export type AppAgentsResponse = AppAgentsResponses[keyof AppAgentsResponses]

export type TuiAppendPromptData = {
  body?: {
    text: string
  }
  path?: never
  query?: {
    directory?: string
  }
  url: "/tui/append-prompt"
}

export type TuiAppendPromptResponses = {
  /**
   * Prompt processed successfully
   */
  200: boolean
}

export type TuiAppendPromptResponse = TuiAppendPromptResponses[keyof TuiAppendPromptResponses]

export type TuiOpenHelpData = {
  body?: never
  path?: never
  query?: {
    directory?: string
  }
  url: "/tui/open-help"
}

export type TuiOpenHelpResponses = {
  /**
   * Help dialog opened successfully
   */
  200: boolean
}

export type TuiOpenHelpResponse = TuiOpenHelpResponses[keyof TuiOpenHelpResponses]

export type TuiOpenSessionsData = {
  body?: never
  path?: never
  query?: {
    directory?: string
  }
  url: "/tui/open-sessions"
}

export type TuiOpenSessionsResponses = {
  /**
   * Session dialog opened successfully
   */
  200: boolean
}

export type TuiOpenSessionsResponse = TuiOpenSessionsResponses[keyof TuiOpenSessionsResponses]

export type TuiOpenThemesData = {
  body?: never
  path?: never
  query?: {
    directory?: string
  }
  url: "/tui/open-themes"
}

export type TuiOpenThemesResponses = {
  /**
   * Theme dialog opened successfully
   */
  200: boolean
}

export type TuiOpenThemesResponse = TuiOpenThemesResponses[keyof TuiOpenThemesResponses]

export type TuiOpenModelsData = {
  body?: never
  path?: never
  query?: {
    directory?: string
  }
  url: "/tui/open-models"
}

export type TuiOpenModelsResponses = {
  /**
   * Model dialog opened successfully
   */
  200: boolean
}

export type TuiOpenModelsResponse = TuiOpenModelsResponses[keyof TuiOpenModelsResponses]

export type TuiSubmitPromptData = {
  body?: never
  path?: never
  query?: {
    directory?: string
  }
  url: "/tui/submit-prompt"
}

export type TuiSubmitPromptResponses = {
  /**
   * Prompt submitted successfully
   */
  200: boolean
}

export type TuiSubmitPromptResponse = TuiSubmitPromptResponses[keyof TuiSubmitPromptResponses]

export type TuiClearPromptData = {
  body?: never
  path?: never
  query?: {
    directory?: string
  }
  url: "/tui/clear-prompt"
}

export type TuiClearPromptResponses = {
  /**
   * Prompt cleared successfully
   */
  200: boolean
}

export type TuiClearPromptResponse = TuiClearPromptResponses[keyof TuiClearPromptResponses]

export type TuiExecuteCommandData = {
  body?: {
    command: string
  }
  path?: never
  query?: {
    directory?: string
  }
  url: "/tui/execute-command"
}

export type TuiExecuteCommandResponses = {
  /**
   * Command executed successfully
   */
  200: boolean
}

export type TuiExecuteCommandResponse = TuiExecuteCommandResponses[keyof TuiExecuteCommandResponses]

export type TuiShowToastData = {
  body?: {
    title?: string
    message: string
    variant: "info" | "success" | "warning" | "error"
  }
  path?: never
  query?: {
    directory?: string
  }
  url: "/tui/show-toast"
}

export type TuiShowToastResponses = {
  /**
   * Toast notification shown successfully
   */
  200: boolean
}

export type TuiShowToastResponse = TuiShowToastResponses[keyof TuiShowToastResponses]

export type AuthSetData = {
  body?: Auth
  path: {
    id: string
  }
  query?: {
    directory?: string
  }
  url: "/auth/{id}"
}

export type AuthSetErrors = {
  /**
   * Bad request
   */
  400: _Error
}

export type AuthSetError = AuthSetErrors[keyof AuthSetErrors]

export type AuthSetResponses = {
  /**
   * Successfully set authentication credentials
   */
  200: boolean
}

export type AuthSetResponse = AuthSetResponses[keyof AuthSetResponses]

export type ClientOptions = {
  baseUrl: `${string}://${string}` | (string & {})
}
