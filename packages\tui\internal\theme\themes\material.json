{"$schema": "https://opencode.ai/theme.json", "defs": {"darkBg": "#263238", "darkBgAlt": "#1e272c", "darkBgPanel": "#37474f", "darkFg": "#eeffff", "darkFgMuted": "#546e7a", "darkRed": "#f07178", "darkPink": "#f78c6c", "darkOrange": "#ffcb6b", "darkYellow": "#ffcb6b", "darkGreen": "#c3e88d", "darkCyan": "#89ddff", "darkBlue": "#82aaff", "darkPurple": "#c792ea", "darkViolet": "#bb80b3", "lightBg": "#fafafa", "lightBgAlt": "#f5f5f5", "lightBgPanel": "#e7e7e8", "lightFg": "#263238", "lightFgMuted": "#90a4ae", "lightRed": "#e53935", "lightPink": "#ec407a", "lightOrange": "#f4511e", "lightYellow": "#ffb300", "lightGreen": "#91b859", "lightCyan": "#39adb5", "lightBlue": "#6182b8", "lightPurple": "#7c4dff", "lightViolet": "#945eb8"}, "theme": {"primary": {"dark": "darkBlue", "light": "lightBlue"}, "secondary": {"dark": "dark<PERSON><PERSON>ple", "light": "lightP<PERSON>ple"}, "accent": {"dark": "dark<PERSON>yan", "light": "lightCyan"}, "error": {"dark": "darkRed", "light": "lightRed"}, "warning": {"dark": "<PERSON><PERSON><PERSON><PERSON>", "light": "light<PERSON>ellow"}, "success": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGreen"}, "info": {"dark": "darkOrange", "light": "lightOrange"}, "text": {"dark": "darkFg", "light": "lightFg"}, "textMuted": {"dark": "darkFgMuted", "light": "lightFgMuted"}, "background": {"dark": "darkBg", "light": "lightBg"}, "backgroundPanel": {"dark": "darkBgAlt", "light": "lightBgAlt"}, "backgroundElement": {"dark": "darkBgPanel", "light": "lightBgPanel"}, "border": {"dark": "#37474f", "light": "#e0e0e0"}, "borderActive": {"dark": "darkBlue", "light": "lightBlue"}, "borderSubtle": {"dark": "#1e272c", "light": "#eeeeee"}, "diffAdded": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGreen"}, "diffRemoved": {"dark": "darkRed", "light": "lightRed"}, "diffContext": {"dark": "darkFgMuted", "light": "lightFgMuted"}, "diffHunkHeader": {"dark": "dark<PERSON>yan", "light": "lightCyan"}, "diffHighlightAdded": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGreen"}, "diffHighlightRemoved": {"dark": "darkRed", "light": "lightRed"}, "diffAddedBg": {"dark": "#2e3c2b", "light": "#e8f5e9"}, "diffRemovedBg": {"dark": "#3c2b2b", "light": "#ffebee"}, "diffContextBg": {"dark": "darkBgAlt", "light": "lightBgAlt"}, "diffLineNumber": {"dark": "#37474f", "light": "#cfd8dc"}, "diffAddedLineNumberBg": {"dark": "#2e3c2b", "light": "#e8f5e9"}, "diffRemovedLineNumberBg": {"dark": "#3c2b2b", "light": "#ffebee"}, "markdownText": {"dark": "darkFg", "light": "lightFg"}, "markdownHeading": {"dark": "darkBlue", "light": "lightBlue"}, "markdownLink": {"dark": "dark<PERSON>yan", "light": "lightCyan"}, "markdownLinkText": {"dark": "dark<PERSON><PERSON>ple", "light": "lightP<PERSON>ple"}, "markdownCode": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGreen"}, "markdownBlockQuote": {"dark": "darkFgMuted", "light": "lightFgMuted"}, "markdownEmph": {"dark": "<PERSON><PERSON><PERSON><PERSON>", "light": "light<PERSON>ellow"}, "markdownStrong": {"dark": "darkOrange", "light": "lightOrange"}, "markdownHorizontalRule": {"dark": "#37474f", "light": "#e0e0e0"}, "markdownListItem": {"dark": "darkBlue", "light": "lightBlue"}, "markdownListEnumeration": {"dark": "dark<PERSON>yan", "light": "lightCyan"}, "markdownImage": {"dark": "dark<PERSON>yan", "light": "lightCyan"}, "markdownImageText": {"dark": "dark<PERSON><PERSON>ple", "light": "lightP<PERSON>ple"}, "markdownCodeBlock": {"dark": "darkFg", "light": "lightFg"}, "syntaxComment": {"dark": "darkFgMuted", "light": "lightFgMuted"}, "syntaxKeyword": {"dark": "dark<PERSON><PERSON>ple", "light": "lightP<PERSON>ple"}, "syntaxFunction": {"dark": "darkBlue", "light": "lightBlue"}, "syntaxVariable": {"dark": "darkFg", "light": "lightFg"}, "syntaxString": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGreen"}, "syntaxNumber": {"dark": "darkOrange", "light": "lightOrange"}, "syntaxType": {"dark": "<PERSON><PERSON><PERSON><PERSON>", "light": "light<PERSON>ellow"}, "syntaxOperator": {"dark": "dark<PERSON>yan", "light": "lightCyan"}, "syntaxPunctuation": {"dark": "darkFg", "light": "lightFg"}}}