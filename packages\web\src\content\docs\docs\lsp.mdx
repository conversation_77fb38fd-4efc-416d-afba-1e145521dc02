---
title: LSP Servers
description: opencode integrates with your LSP servers.
---

opencode integrates with your Language Server Protocol (LSP) to help the LLM interacts with your codebase. It uses diagnostics to provide feedback to the LLM. And _go-to-definition_ and _find-references_ to help navigate your codebase.

---

## Built-in

opencode comes with several built-in LSP servers for popular languages:

| LSP Server | Extensions                                           | Requirements                        |
| ---------- | ---------------------------------------------------- | ----------------------------------- |
| typescript | .ts, .tsx, .js, .jsx, .mjs, .cjs, .mts, .cts         | `typescript` dependency in project  |
| eslint     | .ts, .tsx, .js, .jsx, .mjs, .cjs, .mts, .cts, .vue   | `eslint` dependency in project      |
| gopls      | .go                                                  | `go` command available              |
| ruby-lsp   | .rb, .rake, .gemspec, .ru                            | `ruby` and `gem` commands available |
| pyright    | .py, .pyi                                            | `pyright` dependency installed      |
| elixir-ls  | .ex, .exs                                            | `elixir` command available          |
| zls        | .zig, .zon                                           | `zig` command available             |
| csharp     | .cs                                                  | `.NET SDK` installed                |
| vue        | .vue                                                 | Auto-installs for Vue projects      |
| rust       | .rs                                                  | `rust-analyzer` command available   |
| clangd     | .c, .cpp, .cc, .cxx, .c++, .h, .hpp, .hh, .hxx, .h++ | Auto-installs for C/C++ projects    |

LSP servers are automatically enabled when one of the above file extensions are detected and the requirements are met.

:::note
You can disable automatic LSP server downloads by setting the `OPENCODE_DISABLE_LSP_DOWNLOAD` environment variable to `true`.
:::

---

## How It Works

When opencode opens a file, it:

1. Checks the file extension against all enabled LSP servers.
2. Starts the appropriate LSP server if not already running.

---

## Configure

You can customize LSP servers through the `lsp` section in your opencode config.

```json title="opencode.json"
{
  "$schema": "https://opencode.ai/config.json",
  "lsp": {}
}
```

Each LSP server supports the following:

| Property         | Type     | Description                                       |
| ---------------- | -------- | ------------------------------------------------- |
| `disabled`       | boolean  | Set this to `true` to disable the LSP server      |
| `command`        | string[] | The command to start the LSP server               |
| `extensions`     | string[] | File extensions this LSP server should handle     |
| `env`            | object   | Environment variables to set when starting server |
| `initialization` | object   | Initialization options to send to the LSP server  |

Let's look at some examples.

---

### Disabling LSP servers

To disable a specific LSP server, set `disabled` to `true`:

```json title="opencode.json" {5}
{
  "$schema": "https://opencode.ai/config.json",
  "lsp": {
    "typescript": {
      "disabled": true
    }
  }
}
```

---

### Custom LSP servers

You can add custom LSP servers by specifying the command and file extensions:

```json title="opencode.json" {4-7}
{
  "$schema": "https://opencode.ai/config.json",
  "lsp": {
    "custom-lsp": {
      "command": ["custom-lsp-server", "--stdio"],
      "extensions": [".custom"]
    }
  }
}
```
